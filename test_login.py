#!/usr/bin/env python3
"""
测试登录和聊天功能的脚本
"""

import requests
import json
import time
import websocket
import threading

# API配置
API_BASE = "http://localhost:8000"
WS_BASE = "ws://localhost:8000"

def test_user_login():
    """测试用户登录"""
    print("=== 测试用户登录 ===")

    url = f"{API_BASE}/api/auth/login"
    data = {
        "email": "<EMAIL>",
        "password": "password123"
    }

    try:
        response = requests.post(url, json=data)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")

        if response.status_code == 200:
            session_data = response.json()
            print("✅ 用户登录成功!")
            return session_data
        else:
            print("❌ 用户登录失败!")
            return None

    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return None

def test_get_agents(session_data):
    """测试获取角色列表"""
    print("\n=== 测试获取角色列表 ===")

    if not session_data:
        print("❌ 没有有效的会话数据")
        return None

    url = f"{API_BASE}/api/agents/public-with-creator?limit=10"
    headers = {
        "Authorization": f"Bearer {session_data.get('access_token', '')}"
    }
    
    try:
        response = requests.get(url, headers=headers)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            agents = response.json()
            print(f"✅ 获取到 {len(agents)} 个角色")
            if agents:
                print(f"第一个角色: {agents[0].get('name', 'Unknown')}")
                return agents[0]  # 返回第一个角色用于测试
            return None
        else:
            print(f"❌ 获取角色失败: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return None

def test_create_chat(session_data, agent):
    """测试创建聊天"""
    print("\n=== 测试创建聊天 ===")

    if not session_data or not agent:
        print("❌ 缺少必要的数据")
        return None

    agent_id = agent.get("id")
    user_id = session_data.get("user", {}).get("id")

    url = f"{API_BASE}/api/chats/start-with-agent/{agent_id}?user_id={user_id}"
    headers = {
        "Authorization": f"Bearer {session_data.get('access_token', '')}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.post(url, headers=headers)
        print(f"状态码: {response.status_code}")

        if response.status_code == 200:
            chat = response.json()
            chat_id = chat.get('chat_id')
            print(f"✅ 创建聊天成功! Chat ID: {chat_id}")
            return {"id": chat_id}  # 统一返回格式
        else:
            print(f"❌ 创建聊天失败: {response.text}")
            return None

    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return None

def test_websocket_chat(session_data, chat, agent):
    """测试WebSocket聊天"""
    print("\n=== 测试WebSocket聊天 ===")
    
    if not session_data or not chat or not agent:
        print("❌ 缺少必要的数据")
        return
        
    chat_id = chat.get("id")
    user_id = session_data.get("user", {}).get("id")

    # WebSocket URL
    ws_url = f"{WS_BASE}/ws/chat/{chat_id}?user_id={user_id}"
    
    messages_received = []
    
    def on_message(ws, message):
        print(f"📨 收到消息: {message}")
        messages_received.append(message)
        
        try:
            data = json.loads(message)
            if data.get("type") == "message_chunk":
                print(f"💬 AI回复片段: {data.get('content_chunk', '')}")
            elif data.get("type") == "message_complete":
                print("✅ AI回复完成!")
        except:
            pass
    
    def on_error(ws, error):
        print(f"❌ WebSocket错误: {error}")
    
    def on_close(ws, close_status_code, close_msg):
        print("🔌 WebSocket连接关闭")
    
    def on_open(ws):
        print("🔌 WebSocket连接已建立")
        
        # 发送测试消息
        test_message = {
            "type": "user_message",
            "content": "你好！我想和你聊天。",
            "agent_id": agent.get("id")
        }
        
        print(f"📤 发送消息: {test_message}")
        ws.send(json.dumps(test_message))
    
    try:
        ws = websocket.WebSocketApp(
            ws_url,
            on_open=on_open,
            on_message=on_message,
            on_error=on_error,
            on_close=on_close
        )
        
        # 在单独线程中运行WebSocket
        ws_thread = threading.Thread(target=ws.run_forever)
        ws_thread.daemon = True
        ws_thread.start()
        
        # 等待一段时间让消息处理完成
        print("⏳ 等待AI回复...")
        time.sleep(30)  # 等待30秒
        
        ws.close()
        
        print(f"\n📊 总共收到 {len(messages_received)} 条消息")
        
        if messages_received:
            print("✅ WebSocket聊天测试成功!")
        else:
            print("❌ 没有收到任何消息")
            
    except Exception as e:
        print(f"❌ WebSocket测试异常: {e}")

def main():
    """主测试函数"""
    print("🚀 开始测试聊天功能...")

    # 1. 测试用户登录
    session_data = test_user_login()
    if not session_data:
        print("❌ 登录失败，终止测试")
        return
    
    # 2. 测试获取角色
    agent = test_get_agents(session_data)
    if not agent:
        print("❌ 获取角色失败，终止测试")
        return
    
    # 3. 测试创建聊天
    chat = test_create_chat(session_data, agent)
    if not chat:
        print("❌ 创建聊天失败，终止测试")
        return
    
    # 4. 测试WebSocket聊天
    test_websocket_chat(session_data, chat, agent)
    
    print("\n🎉 测试完成!")

if __name__ == "__main__":
    main()
