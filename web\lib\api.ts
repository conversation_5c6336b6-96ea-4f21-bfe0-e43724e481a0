// API客户端配置
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'
const WS_BASE_URL = process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:8000'

export interface Agent {
  id: string
  name: string
  description?: string
  personality?: string
  scenario?: string
  first_mes?: string
  avatar_url?: string
  image_url?: string
  tags?: string[]
  creator_name?: string
  is_public: boolean
  created_at: string
}

export interface Message {
  id: string
  role: 'user' | 'assistant'
  content: string
  agent_id?: string
  created_at: string
}

export interface Chat {
  id: string
  user_id: string
  story_id?: string
  created_at: string
  updated_at: string
  task_progress?: any
  game_state?: any
}

export interface ChatWithMessages extends Chat {
  messages: Message[]
  participants: Agent[]
}

export class ApiClient {
  private baseUrl: string
  private accessToken: string | null = null

  constructor() {
    this.baseUrl = API_BASE_URL
  }

  // 设置访问令牌
  setAccessToken(token: string | null) {
    this.accessToken = token
  }

  // 获取认证头
  private getAuthHeaders(): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    }

    if (this.accessToken) {
      headers['Authorization'] = `Bearer ${this.accessToken}`
    }

    return headers
  }

  // 获取公开角色列表
  async getPublicAgents(limit = 20): Promise<Agent[]> {
    try {
      const response = await fetch(`${this.baseUrl}/api/agents/public-with-creator?limit=${limit}`)
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      const data = await response.json()
      return data.agents || data || []
    } catch (error) {
      console.error('Failed to fetch public agents:', error)
      throw error
    }
  }

  // 获取角色详情
  async getAgent(agentId: string): Promise<Agent> {
    try {
      const response = await fetch(`${this.baseUrl}/api/agents/${agentId}`, {
        headers: this.getAuthHeaders()
      })
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      return await response.json()
    } catch (error) {
      console.error(`Failed to fetch agent ${agentId}:`, error)
      throw error
    }
  }

  // 开始聊天
  async startChat(agentId: string, userId = 'guest'): Promise<{ chat_id: string }> {
    try {
      const response = await fetch(`${this.baseUrl}/api/chats/start-with-agent/${agentId}?user_id=${userId}`, {
        method: 'POST',
        headers: this.getAuthHeaders()
      })
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      return await response.json()
    } catch (error) {
      console.error('Failed to start chat:', error)
      throw error
    }
  }

  // 获取聊天历史
  async getChatHistory(chatId: string): Promise<Message[]> {
    try {
      const response = await fetch(`${this.baseUrl}/api/chats/${chatId}/messages`)
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      const data = await response.json()
      return data.messages || []
    } catch (error) {
      console.error(`Failed to fetch chat history for ${chatId}:`, error)
      throw error
    }
  }

  // 获取用户聊天列表
  async getUserChats(userId = 'guest', limit = 20): Promise<Chat[]> {
    try {
      const response = await fetch(`${this.baseUrl}/api/user-chats?user_id=${userId}&limit=${limit}`)
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      const data = await response.json()
      return data.chats || []
    } catch (error) {
      console.error('Failed to fetch user chats:', error)
      throw error
    }
  }

  // 创建WebSocket连接
  createWebSocket(chatId: string, userId = 'guest', token?: string): WebSocket {
    // 修复：添加token参数支持WebSocket认证
    let wsUrl = `${WS_BASE_URL}/ws/chat/${chatId}?user_id=${userId}`
    if (token) {
      wsUrl += `&token=${encodeURIComponent(token)}`
    }
    console.log('Creating WebSocket connection to:', wsUrl)
    return new WebSocket(wsUrl)
  }

  // 健康检查
  async healthCheck(): Promise<{ status: string }> {
    try {
      const response = await fetch(`${this.baseUrl}/health`)
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      return await response.json()
    } catch (error) {
      console.error('Health check failed:', error)
      throw error
    }
  }
}

export const apiClient = new ApiClient()